<!DOCTYPE html>
<html>
<head>
    <title>Dashboard</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <header>
        <h1>Dashboard</h1>
    </header>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/fetch_memes">🎭 Fetch Memes</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <div class="navigation">
            <a href="/config">⚙️ Update Config</a>
            <a href="/fetch_memes">🎭 Fetch Memes</a>
            <a href="/review_memes">✏️ Add Text to Memes</a>
            <a href="/generate_videos">🎬 Generate Videos</a>
            <a href="/review_audio">🎵 Review Audio</a>
            <a href="/review_videos">📹 Review Videos</a>
        </div>

        <div id="meme-section">
            <h2>Fetched Memes</h2>
            {% if memes %}
                {% for meme in memes %}
                <div class="meme">
                    <img src="{{ meme.url }}" alt="Meme Image" style="max-width: 400px; max-height: 400px;">
                    <div class="meme-text">
                        <strong>Text:</strong>
                        <p>{{ meme.text or "No text added yet" }}</p>
                    </div>
                    <div class="meme-actions">
                        <a href="/review_memes" class="btn btn-primary">✏️ Add/Edit Text</a>
                        <form method="POST" action="/discard_meme" style="display: inline;">
                            <input type="hidden" name="meme_id" value="{{ meme.id }}">
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to discard this meme?')">🗑️ Discard</button>
                        </form>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <p>No memes fetched yet. <a href="/fetch_memes">Fetch some memes</a> to get started!</p>
            {% endif %}
        </div>

        <div id="video-section">
            <h2>Generated Videos</h2>
            {% for video in videos %}
            <div class="video-item">
                <video controls>
                    <source src="{{ video.video_path }}" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
                <p>{{ video.text }}</p>
                <form method="POST" action="/discard_video">
                    <input type="hidden" name="video_id" value="{{ video.id }}">
                    <button type="submit">Discard</button>
                </form>
            </div>
            {% endfor %}
        </div>

        <h2>Configuration</h2>
        <div class="config">
            <form method="POST" action="/update_config">
                <label for="max_memes">Max Memes (1-100):</label>
                <input type="number" id="max_memes" name="max_memes" min="1" max="100" required>

                <label for="subreddits">Subreddits:</label>
                <div id="subreddits">
                    {% for subreddit in available_subreddits %}
                    <div>
                        <input type="checkbox" id="subreddit_{{ subreddit }}" name="subreddits" value="{{ subreddit }}" {% if subreddit in selected_subreddits %}checked{% endif %}>
                        <label for="subreddit_{{ subreddit }}">{{ subreddit }}</label>
                    </div>
                    {% endfor %}
                    <div>
                        <input type="text" id="new_subreddit" name="new_subreddit" placeholder="Add new subreddit">
                        <button type="button" onclick="addSubreddit()">Add</button>
                    </div>
                </div>

                <button type="submit">Save Settings</button>
            </form>
        </div>
    </div>
    <script>
        function addSubreddit() {
            const newSubredditInput = document.getElementById('new_subreddit');
            const subredditList = document.getElementById('subreddits');

            if (newSubredditInput.value.trim() !== '') {
                const newDiv = document.createElement('div');
                newDiv.innerHTML = `
                    <input type="checkbox" id="subreddit_${newSubredditInput.value}" name="subreddits" value="${newSubredditInput.value}" checked>
                    <label for="subreddit_${newSubredditInput.value}">${newSubredditInput.value}</label>
                `;
                subredditList.appendChild(newDiv);
                newSubredditInput.value = '';
            }
        }
    </script>
</body>
</html>
