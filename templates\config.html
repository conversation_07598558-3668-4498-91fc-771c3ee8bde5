<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <header>
        <h1>Configuration</h1>
    </header>
    <nav>
        <a href="/dashboard">Home</a>
        <a href="/logout">Logout</a>
    </nav>
    <div class="container">
        <h2>Update Configuration</h2>
        <form method="POST" action="/update_config">
            <div class="form-group">
                <label for="max_memes">Max Memes (1-100):</label>
                <input type="number" id="max_memes" name="max_memes" min="1" max="100"
                       value="{{ config.max_memes }}" required>
            </div>

            <div class="form-group">
                <label for="subreddits">Subreddits:</label>
                <div id="subreddits">
                    {% for subreddit in available_subreddits %}
                    <div>
                        <input type="checkbox" id="subreddit_{{ subreddit }}" name="subreddits"
                               value="{{ subreddit }}" {% if subreddit in selected_subreddits %}checked{% endif %}>
                        <label for="subreddit_{{ subreddit }}">{{ subreddit }}</label>
                    </div>
                    {% endfor %}
                    <div>
                        <input type="text" id="new_subreddit" name="new_subreddit" placeholder="Add new subreddit">
                        <button type="button" onclick="addSubreddit()">Add</button>
                    </div>
                </div>
            </div>

            <button type="submit">Save Settings</button>
        </form>

        <div class="current-config">
            <h3>Current Configuration</h3>
            <p><strong>Max Memes:</strong> {{ config.max_memes }}</p>
            <p><strong>Selected Subreddits:</strong> {{ config.subreddits }}</p>
        </div>
    </div>
    <script>
        function addSubreddit() {
            const newSubredditInput = document.getElementById('new_subreddit');
            const subredditList = document.getElementById('subreddits');

            if (newSubredditInput.value.trim() !== '') {
                const newDiv = document.createElement('div');
                newDiv.innerHTML = `
                    <input type="checkbox" id="subreddit_${newSubredditInput.value}" name="subreddits" value="${newSubredditInput.value}" checked>
                    <label for="subreddit_${newSubredditInput.value}">${newSubredditInput.value}</label>
                `;
                subredditList.insertBefore(newDiv, subredditList.lastElementChild);
                newSubredditInput.value = '';
            }
        }
    </script>
</body>
</html>
