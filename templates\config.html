<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <h1>Update Config & Fetch Memes</h1>
    <form method="POST">
        <label for="max_memes">Max Memes:</label>
        <input type="number" id="max_memes" name="max_memes" min="1" max="100" value="10">

        <label for="subreddits">Subreddits:</label>
        <input type="text" id="new_subreddit" name="new_subreddit" placeholder="Add subreddit">
        <button type="submit">Update Config</button>
    </form>

    <h2>Fetched Memes</h2>
    <div id="meme-container">
        {% for meme in memes %}
        <div class="meme">
            <img src="{{ meme.url }}" alt="Meme Image">
            <form method="POST" action="/discard_meme">
                <input type="hidden" name="meme_id" value="{{ meme.id }}">
                <button type="submit">Discard</button>
            </form>
        </div>
        {% endfor %}
    </div>
</body>
</html>
