#!/usr/bin/env python3
"""
Test script for the complete meme-to-video workflow
"""
import os
import tempfile
import requests
from app import generate_audio_from_text, create_video_with_opencv
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)

class MockMeme:
    """Mock meme object for testing"""
    def __init__(self, url, text):
        self.url = url
        self.text = text

def test_audio_generation():
    """Test audio generation"""
    print("🎵 Testing Audio Generation...")
    
    test_text = "This is a test of the audio generation system for memes."
    
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_audio:
        audio_path = temp_audio.name
    
    try:
        success = generate_audio_from_text(test_text, audio_path)
        
        if success and os.path.exists(audio_path):
            file_size = os.path.getsize(audio_path)
            print(f"✅ Audio generation successful!")
            print(f"   File: {audio_path}")
            print(f"   Size: {file_size} bytes")
            return audio_path
        else:
            print("❌ Audio generation failed")
            return None
            
    except Exception as e:
        print(f"❌ Audio generation error: {e}")
        return None

def test_video_generation(audio_path):
    """Test video generation"""
    print("\n🎬 Testing Video Generation...")
    
    # Use a sample meme image URL
    test_meme = MockMeme(
        url="https://i.imgflip.com/1bij.jpg",  # Success Kid meme
        text="Successfully generated a video from a meme!"
    )
    
    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_video:
        video_path = temp_video.name
    
    try:
        success = create_video_with_opencv(test_meme, audio_path, video_path)
        
        if success and os.path.exists(video_path):
            file_size = os.path.getsize(video_path)
            print(f"✅ Video generation successful!")
            print(f"   File: {video_path}")
            print(f"   Size: {file_size} bytes")
            return video_path
        else:
            print("❌ Video generation failed")
            return None
            
    except Exception as e:
        print(f"❌ Video generation error: {e}")
        return None

def cleanup_files(*file_paths):
    """Clean up test files"""
    print("\n🧹 Cleaning up test files...")
    for file_path in file_paths:
        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"   Removed: {file_path}")
            except Exception as e:
                print(f"   Failed to remove {file_path}: {e}")

def main():
    """Run the complete workflow test"""
    print("🚀 Meme-to-Video Workflow Test")
    print("=" * 50)
    
    # Test audio generation
    audio_path = test_audio_generation()
    
    if audio_path:
        # Test video generation
        video_path = test_video_generation(audio_path)
        
        if video_path:
            print("\n🎉 Complete workflow test successful!")
            print("   Both audio and video generation are working.")
            
            # Ask user if they want to keep the files
            try:
                keep_files = input("\nKeep test files? (y/N): ").lower().startswith('y')
                if not keep_files:
                    cleanup_files(audio_path, video_path)
                else:
                    print(f"\nTest files saved:")
                    print(f"   Audio: {audio_path}")
                    print(f"   Video: {video_path}")
            except KeyboardInterrupt:
                cleanup_files(audio_path, video_path)
        else:
            print("\n⚠️ Video generation failed, but audio works.")
            cleanup_files(audio_path)
    else:
        print("\n❌ Audio generation failed. Cannot proceed with video test.")
    
    print("\n" + "=" * 50)
    print("Test completed.")

if __name__ == "__main__":
    main()
