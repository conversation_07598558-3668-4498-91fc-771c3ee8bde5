body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}
header {
    background-color: #007BFF;
    color: white;
    padding: 10px 20px;
    text-align: center;
}
nav {
    background-color: #0056b3;
    padding: 10px;
    text-align: center;
}
nav a {
    color: white;
    margin: 0 15px;
    text-decoration: none;
    font-weight: bold;
}
.container {
    padding: 20px;
}
.meme, .video {
    margin: 20px 0;
    background: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
img, video {
    max-width: 100%;
    border-radius: 5px;
}
.config {
    margin-top: 30px;
    background: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.config label {
    display: block;
    margin: 10px 0 5px;
    font-weight: bold;
}
.config input[type="number"], .config input[type="text"] {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
}
.config button {
    background-color: #007BFF;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
}
.config button:hover {
    background-color: #0056b3;
}
.config div {
    margin-bottom: 10px;
}
