/* Modern CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #10b981;
    --secondary-dark: #059669;
    --danger-color: #ef4444;
    --danger-dark: #dc2626;
    --warning-color: #f59e0b;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-600: #475569;
    --gray-800: #1e293b;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Header Styles */
header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 1.5rem 0;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

header h1 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    position: relative;
    z-index: 1;
}

/* Navigation Styles */
nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    box-shadow: var(--shadow-md);
    border-bottom: 1px solid var(--gray-200);
}

nav .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    padding: 0 1rem;
}

nav a {
    color: var(--gray-600);
    text-decoration: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
    background: white;
    border: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
}

nav a:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Container Styles */
.container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    position: relative;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--gray-800);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

input, textarea, select {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

/* Button Styles */
.btn, button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0.25rem;
}

.btn-primary, button {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.btn-primary:hover, button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    color: white;
}

.btn-danger, .discard-btn {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
    color: white;
}

/* Navigation Grid */
.navigation {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
}

.navigation a {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    color: white;
    padding: 1rem 1.5rem;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
}

.navigation a:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Meme Styles */
.meme, .meme-editor {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.meme:hover, .meme-editor:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.meme img, .meme-image img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1rem;
}

.meme-text {
    margin: 1rem 0;
    padding: 1rem;
    background: var(--gray-100);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.meme-text strong {
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
}

.meme-text p {
    margin-top: 0.5rem;
    color: var(--gray-600);
    font-style: italic;
}

.meme-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.meme-text-editor {
    margin-top: 1rem;
}

.meme-text-editor textarea {
    min-height: 100px;
    resize: vertical;
}

/* Video and Audio Styles */
.video-item, .audio-item {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.video-item:hover, .audio-item:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.video-player video, .audio-player audio {
    width: 100%;
    max-width: 600px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1rem;
}

.video-info, .audio-info {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--gray-100);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--secondary-color);
}

.video-actions, .audio-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

/* Configuration Styles */
.current-config {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
}

.current-config h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.current-config p {
    margin-bottom: 0.5rem;
    color: var(--gray-600);
}

/* Checkbox Styles */
input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
    transform: scale(1.2);
    accent-color: var(--primary-color);
}

/* Actions Section */
.actions {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    text-align: center;
    border: 1px solid var(--gray-200);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 1rem;
        padding: 1rem;
    }

    header h1 {
        font-size: 2rem;
    }

    .navigation {
        grid-template-columns: 1fr;
    }

    .meme-actions, .video-actions, .audio-actions {
        flex-direction: column;
    }

    nav .nav-container {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 2s infinite;
}

/* Success/Error Messages */
.message {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    font-weight: 500;
}

.message-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.message-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.message-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}
