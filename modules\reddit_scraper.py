import praw
import os
import logging
from dotenv import load_dotenv

load_dotenv()  # loads variables from .env into environment

def get_top_memes(limit=10, subreddits=None):
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    username = os.getenv('REDDIT_USERNAME')

    reddit = praw.Reddit(
        client_id=client_id,
        client_secret=client_secret,
        user_agent=f'AutomaticMemeContentGenerator/0.1 by {username}'
    )

    memes = []
    if not subreddits:
        subreddits = ['memes']  # Default subreddit

    for subreddit in subreddits:
        try:
            for submission in reddit.subreddit(subreddit).top(limit=limit):
                if submission.url.endswith(('jpg', 'jpeg', 'png')):
                    memes.append({'url': submission.url, 'title': submission.title})
        except Exception as e:
            logging.error(f"Error fetching memes from subreddit {subreddit}: {e}")

    return memes

if __name__ == '__main__':
    memes = get_top_memes()
    for m in memes:
        print(m['title'], m['url'])
