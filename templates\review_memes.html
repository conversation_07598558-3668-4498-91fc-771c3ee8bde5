<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Review Memes</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <header>
        <h1>Add Text to Memes</h1>
    </header>
    <nav>
        <a href="/dashboard">Home</a>
        <a href="/logout">Logout</a>
    </nav>
    <div class="container">
        {% if memes %}
            {% for meme in memes %}
            <div class="meme-editor">
                <div class="meme-image">
                    <img src="{{ meme.url }}" alt="Meme Image" style="max-width: 400px; max-height: 400px;">
                </div>
                <div class="meme-text-editor">
                    <form method="POST" action="/update_meme_text">
                        <input type="hidden" name="meme_id" value="{{ meme.id }}">
                        <label for="text_{{ meme.id }}">Meme Text:</label>
                        <textarea id="text_{{ meme.id }}" name="text" rows="4" cols="50"
                                  placeholder="Add or edit text for this meme">{{ meme.text or '' }}</textarea>
                        <div class="meme-actions">
                            <button type="submit">Update Text</button>
                        </div>
                    </form>
                    <form method="POST" action="/discard_meme" style="display: inline;">
                        <input type="hidden" name="meme_id" value="{{ meme.id }}">
                        <button type="submit" class="discard-btn"
                                onclick="return confirm('Are you sure you want to discard this meme?')">Discard</button>
                    </form>
                </div>
            </div>
            <hr>
            {% endfor %}

            <div class="actions">
                <a href="/generate_videos" class="btn">Generate Videos from Memes</a>
                <a href="/dashboard" class="btn">Back to Dashboard</a>
            </div>
        {% else %}
            <p>No memes available for editing. <a href="/fetch_memes">Fetch some memes</a> first!</p>
        {% endif %}
    </div>
</body>
</html>
