CREATE TABLE IF NOT EXISTS users (
	id INTEGER PRIMARY KEY AUTOINCREMENT,
	username TEXT NOT NULL UNIQUE,
	password TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS memes (
	id INTEGER PRIMARY KEY AUTOINCREMENT,
	user_id INTEGER NOT NULL,
	url TEXT NOT NULL,
	text TEXT,
	discarded BOOLEAN DEFAULT 0,
	FOREIG<PERSON> KEY (user_id) REFERENCES users (id)
);

CREATE TABLE IF NOT EXISTS configurations (
	id INTEGER PRIMARY KEY AUTOINCREMENT,
	user_id INTEGER NOT NULL,
	max_memes INTEGER DEFAULT 10,
	subreddits TEXT,
	FOR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users (id)
);
