<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Review Audio</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <header>
        <h1>Review Audio</h1>
    </header>
    <nav>
        <a href="/dashboard">Home</a>
        <a href="/logout">Logout</a>
    </nav>
    <div class="container">
        {% if videos %}
            {% for video in videos %}
            <div class="audio-item">
                <div class="audio-player">
                    {% if video.audio_path %}
                    <audio controls>
                        <source src="/{{ video.audio_path }}" type="audio/wav">
                        Your browser does not support the audio tag.
                    </audio>
                    {% else %}
                    <p>No audio file available</p>
                    {% endif %}
                </div>
                <div class="audio-info">
                    <p><strong>Text:</strong> {{ video.text }}</p>
                    <p><strong>Created:</strong> {{ video.created_at.strftime('%Y-%m-%d %H:%M') if video.created_at else 'Unknown' }}</p>
                    <div class="audio-actions">
                        {% if video.audio_path %}
                        <a href="/{{ video.audio_path }}" download class="btn">Download Audio</a>
                        {% endif %}
                        <a href="/review_videos" class="btn">View Video</a>
                    </div>
                </div>
            </div>
            <hr>
            {% endfor %}

            <div class="actions">
                <a href="/generate_videos" class="btn">Generate More Audio</a>
                <a href="/dashboard" class="btn">Back to Dashboard</a>
            </div>
        {% else %}
            <p>No audio files generated yet. <a href="/review_memes">Add text to memes</a> and then <a href="/generate_videos">generate videos</a>!</p>
        {% endif %}
    </div>
</body>
</html>
