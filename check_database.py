#!/usr/bin/env python3
"""
Script to check database persistence and contents
"""
import os
import sqlite3
from datetime import datetime

def check_database():
    """Check if database exists and show its contents"""
    
    # Database path
    base_dir = os.path.abspath(os.path.dirname(__file__))
    db_path = os.path.join(base_dir, "instance", "app.db")
    
    print(f"Checking database at: {db_path}")
    print(f"Database exists: {os.path.exists(db_path)}")
    
    if os.path.exists(db_path):
        print(f"Database size: {os.path.getsize(db_path)} bytes")
        print(f"Last modified: {datetime.fromtimestamp(os.path.getmtime(db_path))}")
        
        # Connect and check tables
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"\nTables in database: {[table[0] for table in tables]}")
            
            # Check each table
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"  {table_name}: {count} records")
                
                # Show sample data for user table
                if table_name == 'user':
                    cursor.execute(f"SELECT id, username FROM {table_name} LIMIT 5")
                    users = cursor.fetchall()
                    for user in users:
                        print(f"    User {user[0]}: {user[1]}")
                        
                # Show sample data for meme table
                elif table_name == 'meme':
                    cursor.execute(f"SELECT id, user_id, url, text FROM {table_name} LIMIT 3")
                    memes = cursor.fetchall()
                    for meme in memes:
                        print(f"    Meme {meme[0]}: User {meme[1]}, URL: {meme[2][:50]}...")
                        
                # Show sample data for configuration table
                elif table_name == 'configuration':
                    cursor.execute(f"SELECT id, user_id, max_memes, subreddits FROM {table_name} LIMIT 3")
                    configs = cursor.fetchall()
                    for config in configs:
                        print(f"    Config {config[0]}: User {config[1]}, Max: {config[2]}, Subreddits: {config[3]}")
            
            conn.close()
            
        except Exception as e:
            print(f"Error reading database: {e}")
    else:
        print("Database file does not exist yet. Run the app first to create it.")

if __name__ == "__main__":
    check_database()
