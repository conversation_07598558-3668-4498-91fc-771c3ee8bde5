from flask import Flask, render_template, request, redirect, url_for, session
from flask_sqlalchemy import SQLAlchemy
import os
from dotenv import load_dotenv
from praw import Reddit
from modules.reddit_scraper import get_top_memes
import logging
import requests
import base64

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'default_secret_key')

# Update the database URI to use an absolute path
base_dir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{os.path.join(base_dir, "instance", "app.db")}'

app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# User model
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(150), nullable=False, unique=True)
    password = db.Column(db.String(150), nullable=False)

# Initialize Reddit API client
reddit = Reddit(
    client_id=os.getenv('REDDIT_CLIENT_ID'),
    client_secret=os.getenv('REDDIT_CLIENT_SECRET'),
    user_agent='Automatic Meme Content Generator'
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)

# Ensure database tables are created
with app.app_context():
    db.create_all()

# Check if user already exists
with app.app_context():
    existing_user = User.query.filter_by(username='<EMAIL>').first()
    if not existing_user:
        user = User(username='<EMAIL>', password='dhanush21feb@Amcg')
        db.session.add(user)
        db.session.commit()
        print("User credentials added to the database.")
    else:
        print("User already exists in the database.")

# Routes
@app.route('/')
def home():
    return render_template('login.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username, password=password).first()
        if user:
            session['user_id'] = user.id
            return redirect(url_for('dashboard'))
        else:
            return 'Invalid credentials'
    return render_template('login.html')

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('dashboard.html')

@app.route('/logout')
def logout():
    session.pop('user_id', None)
    return redirect(url_for('login'))

def extract_text_from_image(image_url):
    try:
        response = requests.get(image_url)
        image_data = response.content
        # Encode image data to Base64
        encoded_image = base64.b64encode(image_data).decode('utf-8')

        ollama_api_url = os.getenv('OLLAMA_API_URL')
        headers = {'Authorization': f'Bearer {os.getenv("OLLAMA_API_KEY")}', 'Content-Type': 'application/json'}
        payload = {'model': 'phi3', 'image': encoded_image}

        ollama_response = requests.post(ollama_api_url, headers=headers, json=payload)
        ollama_response.raise_for_status()
        result = ollama_response.json()
        return result.get('text', '').strip()
    except Exception as e:
        logging.error(f"Error extracting text with Ollama: {e}")
        return ""

# Route to fetch memes from Reddit
@app.route('/fetch_memes')
def fetch_memes():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    memes = []
    for meme in get_top_memes(limit=10):
        text = extract_text_from_image(meme['url'])
        memes.append({'url': meme['url'], 'text': text})

    logging.debug(f"Fetched memes: {memes}")  # Log the fetched memes
    return render_template('dashboard.html', memes=memes)

# Define missing routes
@app.route('/config')
def config():
    return render_template('config.html')

@app.route('/review_memes')
def review_memes():
    return render_template('review_memes.html')

@app.route('/review_audio')
def review_audio():
    return render_template('review_audio.html')

@app.route('/review_videos')
def review_videos():
    return render_template('review_videos.html')

if __name__ == '__main__':
    app.run(debug=True)
