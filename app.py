from flask import Flask, render_template, request, redirect, url_for, session
from flask_sqlalchemy import SQLAlchemy
import os
from dotenv import load_dotenv
from praw import Reddit
from modules.reddit_scraper import get_top_memes
from modules.ocr import extract_text_from_image
import logging
import requests
import base64
import json
import pyttsx3
from moviepy.editor import ImageClip, AudioFileClip, CompositeVideoClip
import tempfile
import uuid

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'default_secret_key')

# Update the database URI to use an absolute path
base_dir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{os.path.join(base_dir, "instance", "app.db")}'

app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# Database Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(150), nullable=False, unique=True)
    password = db.Column(db.String(150), nullable=False)

class Meme(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    url = db.Column(db.String(500), nullable=False)
    text = db.Column(db.Text)
    discarded = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())

class Configuration(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    max_memes = db.Column(db.Integer, default=10)
    subreddits = db.Column(db.Text, default='memes')

class Video(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    meme_id = db.Column(db.Integer, db.ForeignKey('meme.id'), nullable=False)
    video_path = db.Column(db.String(500), nullable=False)
    audio_path = db.Column(db.String(500))
    text = db.Column(db.Text)
    discarded = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())

# Initialize Reddit API client
reddit = Reddit(
    client_id=os.getenv('REDDIT_CLIENT_ID'),
    client_secret=os.getenv('REDDIT_CLIENT_SECRET'),
    user_agent='Automatic Meme Content Generator'
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)

# Ensure database tables are created
with app.app_context():
    db.create_all()

# Check if user already exists
with app.app_context():
    existing_user = User.query.filter_by(username='<EMAIL>').first()
    if not existing_user:
        user = User(username='<EMAIL>', password='dhanush21feb@Amcg')
        db.session.add(user)
        db.session.commit()
        print("User credentials added to the database.")
    else:
        print("User already exists in the database.")

# Routes
@app.route('/')
def home():
    return render_template('login.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username, password=password).first()
        if user:
            session['user_id'] = user.id
            return redirect(url_for('dashboard'))
        else:
            return 'Invalid credentials'
    return render_template('login.html')

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('dashboard.html')

@app.route('/logout')
def logout():
    session.pop('user_id', None)
    return redirect(url_for('login'))

# Helper functions
def get_user_config(user_id):
    """Get user configuration or create default"""
    config = Configuration.query.filter_by(user_id=user_id).first()
    if not config:
        config = Configuration(user_id=user_id)
        db.session.add(config)
        db.session.commit()
    return config

def save_meme_to_db(user_id, url, text=""):
    """Save meme to database"""
    meme = Meme(user_id=user_id, url=url, text=text)
    db.session.add(meme)
    db.session.commit()
    return meme

# Route to fetch memes from Reddit
@app.route('/fetch_memes')
def fetch_memes():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']
    config = get_user_config(user_id)

    # Parse subreddits from config
    subreddits = [s.strip() for s in config.subreddits.split(',') if s.strip()]

    # Clear existing memes for this user
    Meme.query.filter_by(user_id=user_id).delete()
    db.session.commit()

    memes_data = []
    reddit_memes = get_top_memes(limit=config.max_memes, subreddits=subreddits)

    for meme_info in reddit_memes:
        try:
            # Extract text from image
            text = extract_text_from_image(meme_info['url'])

            # Save to database
            meme = save_meme_to_db(user_id, meme_info['url'], text)

            memes_data.append({
                'id': meme.id,
                'url': meme.url,
                'text': meme.text,
                'discarded': meme.discarded
            })
        except Exception as e:
            logging.error(f"Error processing meme {meme_info['url']}: {e}")
            continue

    logging.info(f"Fetched {len(memes_data)} memes for user {user_id}")
    return render_template('dashboard.html', memes=memes_data)

# Configuration routes
@app.route('/config')
def config():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']
    config = get_user_config(user_id)

    available_subreddits = ['memes', 'dankmemes', 'wholesomememes', 'memeeconomy', 'prequelmemes']
    selected_subreddits = [s.strip() for s in config.subreddits.split(',') if s.strip()]

    return render_template('config.html',
                         config=config,
                         available_subreddits=available_subreddits,
                         selected_subreddits=selected_subreddits)

@app.route('/update_config', methods=['POST'])
def update_config():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']
    config = get_user_config(user_id)

    # Update configuration
    config.max_memes = min(100, max(1, int(request.form.get('max_memes', 10))))

    # Handle subreddits
    selected_subreddits = request.form.getlist('subreddits')
    new_subreddit = request.form.get('new_subreddit', '').strip()
    if new_subreddit:
        selected_subreddits.append(new_subreddit)

    config.subreddits = ','.join(selected_subreddits) if selected_subreddits else 'memes'

    db.session.commit()

    return redirect(url_for('config'))

@app.route('/review_memes')
def review_memes():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']
    memes = Meme.query.filter_by(user_id=user_id, discarded=False).all()

    return render_template('review_memes.html', memes=memes)

@app.route('/update_meme_text', methods=['POST'])
def update_meme_text():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']
    meme_id = request.form.get('meme_id')
    new_text = request.form.get('text', '')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        meme.text = new_text
        db.session.commit()

    return redirect(url_for('review_memes'))

@app.route('/discard_meme', methods=['POST'])
def discard_meme():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']
    meme_id = request.form.get('meme_id')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        meme.discarded = True
        db.session.commit()

    return redirect(url_for('review_memes'))

# Audio and Video generation functions
def generate_audio_from_text(text, output_path):
    """Generate audio file from text using TTS"""
    try:
        engine = pyttsx3.init()
        engine.setProperty('rate', 150)  # Speed of speech
        engine.setProperty('volume', 0.9)  # Volume level

        engine.save_to_file(text, output_path)
        engine.runAndWait()
        return True
    except Exception as e:
        logging.error(f"Error generating audio: {e}")
        return False

def create_video_from_meme(meme, audio_path, output_path):
    """Create video from meme image and audio"""
    try:
        # Download image
        response = requests.get(meme.url, timeout=10)
        response.raise_for_status()

        # Save image temporarily
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_img:
            temp_img.write(response.content)
            temp_img_path = temp_img.name

        # Create video clip
        audio_clip = AudioFileClip(audio_path)
        image_clip = ImageClip(temp_img_path, duration=audio_clip.duration)

        # Resize image to standard video size
        image_clip = image_clip.resize(height=720)

        # Combine image and audio
        video = CompositeVideoClip([image_clip])
        video = video.set_audio(audio_clip)

        # Write video file
        video.write_videofile(output_path, fps=24, codec='libx264', audio_codec='aac')

        # Cleanup
        os.unlink(temp_img_path)
        video.close()
        audio_clip.close()

        return True
    except Exception as e:
        logging.error(f"Error creating video: {e}")
        return False

@app.route('/generate_videos')
def generate_videos():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']
    memes = Meme.query.filter_by(user_id=user_id, discarded=False).all()

    # Create directories if they don't exist
    os.makedirs('static/audio', exist_ok=True)
    os.makedirs('static/videos', exist_ok=True)

    generated_count = 0
    for meme in memes:
        if not meme.text or meme.text.strip() == "":
            continue

        try:
            # Generate unique filenames
            audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
            video_filename = f"video_{meme.id}_{uuid.uuid4().hex[:8]}.mp4"

            audio_path = os.path.join('static', 'audio', audio_filename)
            video_path = os.path.join('static', 'videos', video_filename)

            # Generate audio
            if generate_audio_from_text(meme.text, audio_path):
                # Generate video
                if create_video_from_meme(meme, audio_path, video_path):
                    # Save video record to database
                    video_record = Video(
                        user_id=user_id,
                        meme_id=meme.id,
                        video_path=video_path,
                        audio_path=audio_path,
                        text=meme.text
                    )
                    db.session.add(video_record)
                    generated_count += 1

        except Exception as e:
            logging.error(f"Error generating video for meme {meme.id}: {e}")
            continue

    db.session.commit()
    logging.info(f"Generated {generated_count} videos for user {user_id}")

    return redirect(url_for('review_videos'))

@app.route('/review_audio')
def review_audio():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']
    videos = Video.query.filter_by(user_id=user_id, discarded=False).all()

    return render_template('review_audio.html', videos=videos)

@app.route('/review_videos')
def review_videos():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']
    videos = Video.query.filter_by(user_id=user_id, discarded=False).all()

    return render_template('review_videos.html', videos=videos)

@app.route('/discard_video', methods=['POST'])
def discard_video():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    user_id = session['user_id']
    video_id = request.form.get('video_id')

    video = Video.query.filter_by(id=video_id, user_id=user_id).first()
    if video:
        video.discarded = True
        db.session.commit()

    return redirect(url_for('review_videos'))

if __name__ == '__main__':
    app.run(debug=True)
