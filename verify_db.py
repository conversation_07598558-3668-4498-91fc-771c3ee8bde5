from sqlalchemy import create_engine, inspect
import os
from app import app, db, User

# Database path
base_dir = os.path.abspath(os.path.dirname(__file__))
db_path = os.path.join(base_dir, 'instance', 'app.db')

# Connect to the database
engine = create_engine(f"sqlite:///{db_path}")
inspector = inspect(engine)

# List tables
tables = inspector.get_table_names()
print("Tables in the database:", tables)

# Check columns in 'users' table
columns = inspector.get_columns('users')
print("Columns in 'users' table:", columns)

# Add user credentials to the database
with app.app_context():
    user = User(username='<EMAIL>', password='dhanush21feb@Amcg')
    db.session.add(user)
    db.session.commit()
    print("User credentials added to the database.")
