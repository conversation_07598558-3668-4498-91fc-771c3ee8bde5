#!/usr/bin/env python3
"""
Test script for audio generation using pyttsx3
"""
import pyttsx3
import os
import tempfile

def test_audio_generation():
    """Test if pyttsx3 can generate audio files"""
    
    print("Testing pyttsx3 audio generation...")
    
    try:
        # Initialize the TTS engine
        engine = pyttsx3.init()
        
        # Set properties
        engine.setProperty('rate', 150)  # Speed of speech
        engine.setProperty('volume', 0.9)  # Volume level
        
        # Get available voices
        voices = engine.getProperty('voices')
        print(f"Available voices: {len(voices)}")
        for i, voice in enumerate(voices):
            print(f"  Voice {i}: {voice.name} - {voice.id}")
        
        # Test text
        test_text = "Hello! This is a test of the text-to-speech functionality for the meme generator."
        
        # Create output file
        output_file = os.path.join(tempfile.gettempdir(), "test_audio.wav")
        
        print(f"Generating audio file: {output_file}")
        print(f"Text: {test_text}")
        
        # Generate audio
        engine.save_to_file(test_text, output_file)
        engine.runAndWait()
        
        # Check if file was created
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"✅ Audio file created successfully!")
            print(f"   File: {output_file}")
            print(f"   Size: {file_size} bytes")
            
            # Clean up
            os.remove(output_file)
            print("   Test file cleaned up.")
            
            return True
        else:
            print("❌ Audio file was not created")
            return False
            
    except Exception as e:
        print(f"❌ Error during audio generation: {e}")
        return False

def test_different_voices():
    """Test different voices if available"""
    
    print("\nTesting different voices...")
    
    try:
        engine = pyttsx3.init()
        voices = engine.getProperty('voices')
        
        if len(voices) > 1:
            # Test with different voice
            engine.setProperty('voice', voices[1].id)
            
            test_text = "This is a test with a different voice."
            output_file = os.path.join(tempfile.gettempdir(), "test_voice2.wav")
            
            engine.save_to_file(test_text, output_file)
            engine.runAndWait()
            
            if os.path.exists(output_file):
                print(f"✅ Alternative voice test successful!")
                os.remove(output_file)
                return True
            else:
                print("❌ Alternative voice test failed")
                return False
        else:
            print("ℹ️ Only one voice available, skipping alternative voice test")
            return True
            
    except Exception as e:
        print(f"❌ Error testing alternative voice: {e}")
        return False

if __name__ == "__main__":
    print("🎵 Audio Generation Test Suite")
    print("=" * 40)
    
    # Test basic functionality
    basic_test = test_audio_generation()
    
    # Test different voices
    voice_test = test_different_voices()
    
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    print(f"   Basic Audio Generation: {'✅ PASS' if basic_test else '❌ FAIL'}")
    print(f"   Voice Variation Test:   {'✅ PASS' if voice_test else '❌ FAIL'}")
    
    if basic_test and voice_test:
        print("\n🎉 All tests passed! Audio generation is working properly.")
    else:
        print("\n⚠️ Some tests failed. Check the error messages above.")
