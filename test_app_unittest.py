import unittest
from app import app, db, User
from modules.reddit_scraper import get_top_memes

class AppTestCase(unittest.TestCase):

    def setUp(self):
        app.config['TESTING'] = True
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        app.config['SECRET_KEY'] = 'test_secret_key'
        self.app = app.test_client()
        with app.app_context():
            db.create_all()
            user = User(username='testuser', password='testpassword')
            db.session.add(user)
            db.session.commit()

    def tearDown(self):
        with app.app_context():
            db.session.remove()
            db.drop_all()

    def test_login_valid(self):
        response = self.app.post('/login', data={'username': 'testuser', 'password': 'testpassword'})
        self.assertEqual(response.status_code, 302)  # Redirect to dashboard

    def test_login_invalid(self):
        response = self.app.post('/login', data={'username': 'wronguser', 'password': 'wrongpassword'})
        self.assertIn(b'Invalid credentials', response.data)

    def test_fetch_memes(self):
        memes = get_top_memes(limit=5)
        self.assertTrue(len(memes) > 0)
        self.assertIn('url', memes[0])
        self.assertIn('title', memes[0])

if __name__ == '__main__':
    unittest.main()
